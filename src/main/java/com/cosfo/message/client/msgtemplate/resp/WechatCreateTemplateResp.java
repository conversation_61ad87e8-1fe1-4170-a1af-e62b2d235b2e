package com.cosfo.message.client.msgtemplate.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date : 2023/2/15 19:50
 * 创建个人消息模板返回参数
 */
@Data
public class WechatCreateTemplateResp implements Serializable {
    private static final long serialVersionUID = -4768303217210257718L;
    /**
     * 错误信息
     */
    private String errMsg;
    /**
     * 错误码 0表示成功，其余表示失败
     */
    private Integer errCode;
    /**
     * 添加至帐号下的模板id
     */
    private String priTmplId;
    /**
     * 商户id
     */
    private Long tenantId;
    /**
     * 商户创建的个人模板id
     */
    private Long id;
}
