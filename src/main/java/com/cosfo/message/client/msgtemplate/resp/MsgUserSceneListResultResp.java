package com.cosfo.message.client.msgtemplate.resp;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 *     <p>
 *     客户维度消息场景Resp
 *     <p/>
 * <AUTHOR>
 * @since 2023-02-09
 * 客户维度消息场景列表Resp
 */
@Data
public class MsgUserSceneListResultResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     * 场景ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间（推送权限变更时间）
     */
    private LocalDateTime updateTime;

    /**
     * 关键字
     */
    private List<KeyWordResultResp> keywords;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 场景 状态0=不可用（禁用）;1=可用(启用中)
     */
    private Integer sceneStatus;

    /**
     * 客户维度的推送功能启用、禁用
     * 场景推送权限 状态0=禁用推送;1=启用推送
     */
    private Integer availableStatus;

    /**
     * 操作人（状态变更人）
     */
    private Long updater;

}
