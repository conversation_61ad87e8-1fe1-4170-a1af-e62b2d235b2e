package com.cosfo.message.client.msgtemplate.provider;

import com.cosfo.message.client.msgtemplate.req.PrivateMsgTemplateWechatReq;
import com.cosfo.message.client.msgtemplate.resp.WechatCreateAllTemplateResp;
import com.cosfo.message.client.msgtemplate.resp.WechatCreateTemplateResp;
import net.xianmu.common.result.DubboResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @date : 2023/2/9 17:36
 * 消息模板操作服务
 *
 */
public interface MsgTemplateCommandProvider {

    /**
     * 未关联小程序(商户)单独创建模板
     */
    DubboResponse<Long> createTemplate(PrivateMsgTemplateWechatReq privateMsgTemplateWechatReq);

    /**
     * 未关联小程序(商户)创建微信模板
     * @param privateMsgTemplateWechatReq
     * @return
     */
    DubboResponse<WechatCreateTemplateResp> createWeiXinTemplate(PrivateMsgTemplateWechatReq privateMsgTemplateWechatReq);

    /**
     * 未关联小程序全部创建模板
     * @param privateMsgTemplateWechatReq
     * @return
     */
    DubboResponse<WechatCreateAllTemplateResp> createAllTemplateList(PrivateMsgTemplateWechatReq privateMsgTemplateWechatReq);

    /**
     * 创建失败小程序重新创建模板
     */
    DubboResponse<WechatCreateTemplateResp> createTemplateRetry(Long id, String accessToken);

    /**
     * 刷新个人模板的生效状态
     * @param id 私有模板id
     * @return
     */
    DubboResponse refreshApp(Long id, String accessToken);


}
