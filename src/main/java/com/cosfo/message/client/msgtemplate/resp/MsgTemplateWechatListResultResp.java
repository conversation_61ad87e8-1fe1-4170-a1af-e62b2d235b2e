package com.cosfo.message.client.msgtemplate.resp;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date : 2023/2/10 15:20
 * 模板池列表返回Resp
 */
@Data
public class MsgTemplateWechatListResultResp implements Serializable {


    private static final long serialVersionUID = -8484744039106362048L;
    /**
     * primary key（模板id）
     */
    private Long id;

    /**
     * 微信模版名称(标题、小程序模板标题)
     */
    private String wechatTitle;

    /**
     * 关键词
     */
    private List<KeyWordResultResp> keywords;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 已关联场景数
     */
    private Integer sceneCount;

    /**
     * 已关联模板数
     */
    private Integer linkTemplateCount;

    /**
     * 已失效模板数
     */
    private Integer failTemplateCount;

    /**
     * 父模版id
     */
    private Long pId;

    /**
     * 创建人id
     */
    private Long creator;


}
