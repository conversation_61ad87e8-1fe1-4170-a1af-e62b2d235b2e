package com.cosfo.message.client.msgtemplate.req;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date : 2023/2/8 18:42
 * 客户维度场景列表查询入参req
 */
@Data
public class MsgSceneUserWechatReq implements Serializable {
    private static final long serialVersionUID = -3637856445050641403L;

    /**
     * primary key（场景id）
     */
    private Long id;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 帆台维度的启用、禁用
     * 场景 状态0=不可用（禁用）;1=可用（启用中）
     */
    private Integer sceneStatus;

    /**
     * 用户id
     */
    private Long uId;

    /**
     * 帆台权限变更人id
     */
    private Long tenantId;

    /**
     * 客户维度的推送功能启用、禁用
     * 场景推送权限 状态0=禁用推送;1=启用推送
     */
    private Integer availableStatus;

    private Integer pageIndex;

    private Integer pageSize;


}
