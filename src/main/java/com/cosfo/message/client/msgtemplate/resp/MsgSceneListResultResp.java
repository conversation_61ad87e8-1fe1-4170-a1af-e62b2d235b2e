package com.cosfo.message.client.msgtemplate.resp;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date : 2023/2/10 16:25
 * 帆台场景列表返回Resp
 */
@Data
public class MsgSceneListResultResp implements Serializable {

    private static final long serialVersionUID = 7683161022422824503L;

    /**
     * primary key
     * 场景ID
     */
    private Long id;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 模版池id
     * 关联帆台模板ID
     */
    private Long templateId;

    /**
     * 模版名称
     * 关联帆台模板标题
     */
    private String templateName;


    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 字段数
     */
    private Integer sceneCount;

    /**
     * 操作人
     */
    private Long updater;
}

