package com.cosfo.message.client.msgtemplate.resp;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date : 2023/2/10 15:20
 * 模板池列表返回Resp
 */
@Data
public class MsgTemplateWechatDetailResultResp implements Serializable {


    private static final long serialVersionUID = -8484744039106362049L;
    /**
     * primary key（模板id）
     */
    private Long id;

    /**
     * 微信模版名称(标题、小程序模板标题)
     */
    private String wechatTitle;

    /**
     * 场景说明
     */
    private String scene;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 模板关键词
     */
    private List<KeyWordResultResp> keywords;



}
