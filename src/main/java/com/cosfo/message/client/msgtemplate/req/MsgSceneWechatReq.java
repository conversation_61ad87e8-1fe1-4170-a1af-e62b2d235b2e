package com.cosfo.message.client.msgtemplate.req;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date : 2023/2/10 9:56
 */
@Data
public class MsgSceneWechatReq implements Serializable {

    private static final long serialVersionUID = 1242816780859437934L;
    /**
     * primary key（场景id）
     */
    private Long id;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 是否关联模板
     * 0未关联 1关联
     */
    private Integer privateFlag;

    /**
     * 帆台维度的启用、禁用
     * 场景 状态0=不可用（禁用）;1=可用（启用中）
     */
    private Integer sceneStatus;

    /**
     * 当前页码
     */
    private Integer pageIndex;

    /**
     * 页面大小
     */
    private Integer pageSize;

    /**
     * 更新场景时使用
     * 消息详情url（路径）
     */
    private String page;

    /**
     * 更新操作人，更新时传入
     */
    private Long updater;
}
