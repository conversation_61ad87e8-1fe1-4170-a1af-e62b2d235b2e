package com.cosfo.message.client.msgtemplate.resp;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class DemoResultResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * demoId
     */
    private Long demoId;

    /**
     * demo名称
     */
    private String demoName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private Integer updater;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 删除标识
     */
    private Integer delFlag;

}
