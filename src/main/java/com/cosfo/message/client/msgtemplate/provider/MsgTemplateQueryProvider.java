package com.cosfo.message.client.msgtemplate.provider;

import com.cosfo.message.client.msgtemplate.req.*;
import com.cosfo.message.client.msgtemplate.resp.*;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.DubboResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @date : 2023/2/9 17:36
 * 消息模板查询服务
 *
 */
public interface MsgTemplateQueryProvider {
    /**
     * 获取微信公共模板池列表
     * @param msgTemplateWechatPublicReq
     * @return
     */
    DubboResponse<PageInfo<MsgPublicTemplateWechatListResultResp>> getWechatPublicMsgTemplateListByCondition(MsgTemplateWechatPublicReq msgTemplateWechatPublicReq);

    /**
     * 按条件获取获取帆台模板池列表
     * @param wechatMsgTemplateReq
     * @return
     */
    DubboResponse<PageInfo<MsgTemplateWechatListResultResp>> getWechatMsgTemplateListByCondition(WechatMsgTemplateReq wechatMsgTemplateReq);


    /**
     * 按模板id获取小程序信息
     * @param id
     * @return
     */
    DubboResponse<MsgTemplateToAppResultResp> getWechatAppInfoByTemplateId(Long id);

    /**
     * 按id获取帆台模板详情
     * @param id
     * @return
     */
    DubboResponse<MsgTemplateWechatDetailResultResp> getWechatMsgTemplateDetailByPublicTemplateId(Long id);

    /**
     * 按模板id获取所有已关联商城的id
     * @param id 模板id
     * @return
     */
    DubboResponse<List<Long>> getRelateIdListByTemplateId(Long id);

    /**
     * 按模板状态获取相应帆台模板id下的子模板idList
     * @param fanTaiTemplateId 帆台模板id
     * @param availableStatus 模板状态
     * @return
     */
    DubboResponse<List<Long>> getTemplateIdListByStatus(Long fanTaiTemplateId, Integer availableStatus);

    /**
     * 根据id获取微信公共模板信息
     * @param id
     * @return
     */
    DubboResponse<MsgPublicTemplateWechatListResultResp> getWechatPublicMsgTemplateById(Long id);


    /**
     * 按条件查询微信公共模板
     * @param wechatMsgTemplateQueryReq
     * @return
     */
    DubboResponse<List<MsgPublicTemplateWechatListResultResp>> getWechatPublicMsgTemplateByCondition(WechatMsgTemplateQueryReq wechatMsgTemplateQueryReq);

    /**
     * 按模板id和tenantId去获取个人模板列表
     * @param id
     * @param tenantIdList
     * @return
     */
    DubboResponse<List<MsgTemplateToAppResultResp>> getWechatListByTenantIdList(Long id, List<Long> tenantIdList);

    /**
     * 查询已失败的模板列表
     * @param msgWechatAppReq
     * @return
     */
    DubboResponse<List<MsgTemplateWechatAppListResultResp>> getFailTemplateListByCondition(MsgWechatAppReq msgWechatAppReq);

    /**
     * 传入一个tenantId列表，返回可用的tenantId列表
     * @param fanTaiTemplateId 帆台模板池id
     * @param tenantIdList 租户id列表
     * @param availableStatus 个人模板状态
     * @return
     */
    DubboResponse<List<Long>> getEnableTenantIdListByTenantIdList(Long fanTaiTemplateId, List<Long> tenantIdList, Integer availableStatus);

    /**
     * 按id列表查询模板池模板
     * @param idList
     * @return
     */
    DubboResponse<List<MsgPublicTemplateWechatListResultResp>> getWechatPublicMsgTemplateByIdList(List<Long> idList);
}
