package com.cosfo.message.client.msgtemplate.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date : 2023/2/10 15:21
 */
@Data
public class KeyWordResultResp implements Serializable {
    /**
     * 关键词id
     */
    private Integer kid;
    /**
     * 关键词名称
     */
    private String name;
    /**
     * 关键词示例值
     */
    private String example;
    /**
     * 帆台 关键字对应字段名称
     * 模板的关键词名称
     */
    private String filedName;
    /**
     * 关键词序号，创建模板时需要传入
     */
    private Integer sort;

    /**
     * 字段属性
     */
    private String rule;
}
