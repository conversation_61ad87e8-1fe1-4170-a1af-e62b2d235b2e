package com.cosfo.message.client.msgtemplate.req;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date : 2023/2/1 15:12
 * 商户创建帆台消息模板入参
 */
@Data
public class PrivateMsgTemplateWechatReq implements Serializable {
    private static final long serialVersionUID = 1776296290222072027L;
    /**
     * 模板id
     */
    private Long id;

    /**
     * 创建模板时选用的关键字序号列表
     */
    private List<Integer> kidList;

    /**
     * 商户id，为单个商户创建模板时使用
     * 不传表示未关联商户全部创建
     */
    private Long tenantId;

    /**
     * 帆台用户id
     */
    private Long uId;

    /**
     * 场景说明
     */
    private String scene;

    /**
     * 创建模板的商户信息
     */
    private List<WechatAuthorReq> wechatAuthorReqList;
}
