package com.cosfo.message.client.msgtemplate.resp;


import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 微信消息模版关联场景明细Resp
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-31
 */
@Data
public class MsgSceneWechatDetailResultResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key（帆台模板id）
     */
    private Long id;


    /**
     * 微信模版名称(模板标题)
     */
    private String wechatTitle;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 场景说明
     */
    private String scene;

    /**
     * 关联场景数
     */
    private Integer countScene;

    /**
     * 场景列表
     */
    List<MsgSceneDetailResultResp> msgSceneList;

}
