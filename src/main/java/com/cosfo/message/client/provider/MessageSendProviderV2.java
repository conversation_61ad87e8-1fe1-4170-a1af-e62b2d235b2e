package com.cosfo.message.client.provider;

import com.cosfo.message.client.enums.ChannelTypeEnum;
import com.cosfo.message.client.req.MessageBodyReq;
import com.cosfo.message.client.req.MessageUserReq;
import com.cosfo.message.client.resp.MsgSendLogResp;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.result.DubboResponse;

import java.util.List;

/**
 * 消息发送
 */
public interface MessageSendProviderV2 {

    /**
     * 批量发送消息(单次最大50条)
     *
     * @param tenantId        租户ID
     * @param channelTypeEnum 4, "公众号消息通知"
     * @param channelCode     公众号编码  "公众号消息通知"时不可为空 ，
     * @param sender          发送者
     * @param receiverList    接收者
     * @param mesg            消息内容
     * @return 消息发送记录id
     */
    DubboResponse<List<MsgSendLogResp>> batchSendMessage(Long tenantId, ChannelTypeEnum channelTypeEnum, String channelCode, MessageUserReq sender,
                                                         List<MessageUserReq> receiverList, MessageBodyReq mesg);

    /**
     * 批量发送消息(单次最大50条)
     *
     * @param tenantId        租户ID
     * @param channelTypeEnum 4, "公众号消息通知"
     * @param channelCode     公众号编码  "公众号消息通知"时不可为空 ，
     * @param sender          发送者
     * @param receiverList    接收者
     * @param mesg            消息内容
     * @param systemOriginEnum 系统来源
     * @return 消息发送记录id
     */
    DubboResponse<List<MsgSendLogResp>> batchSendMessageByThridUid(Long tenantId, ChannelTypeEnum channelTypeEnum, String channelCode, String sender, List<String> receiverList, MessageBodyReq mesg, SystemOriginEnum systemOriginEnum);

}
