package com.cosfo.message.client.provider;

import com.cosfo.message.client.enums.ChannelTypeEnum;
import com.cosfo.message.client.req.MessageBodyReq;
import com.cosfo.message.client.req.MessageUserReq;
import com.cosfo.message.client.resp.MsgSendLogResp;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.result.DubboResponse;

import java.util.List;

/**
 * 消息发送
 */
public interface MessageSendProvider {

    /**
     * 批量发送消息(单次最大50条)
     * @param channelTypeEnum 2=飞书
     * @param sender 发送者
     * @param receiverList 接收者
     * @param mesg 消息内容
     * @param systemOriginEnum 系统来源 @Link net.xianmu.common.enums.base.auth.SystemOriginEnum
     * @return 消息发送记录id
     */
    DubboResponse<List<MsgSendLogResp>> batchSendMessageByThridUid(Long tenantId, ChannelTypeEnum channelTypeEnum, String sender, List<String> receiverList, MessageBodyReq mesg, SystemOriginEnum systemOriginEnum);

    /**
     * 发送消息
     * @param channelTypeEnum 2=飞书
     * @param sender
     * @return
     */
    DubboResponse<MsgSendLogResp> sendMessageByThridUid(Long tenantId,ChannelTypeEnum channelTypeEnum, String sender, String receiver, MessageBodyReq mesg, SystemOriginEnum systemOriginEnum);
    /**
     * 批量发送消息(单次最大50条)
     * @param channelTypeEnum 2=飞书
     * @param sender 发送者
     * @param receiverList 接收者
     * @param mesg 消息内容
     * @return 消息发送记录id
     */
    DubboResponse<List<MsgSendLogResp>> batchSendMessage(Long tenantId,ChannelTypeEnum channelTypeEnum, MessageUserReq sender,List<MessageUserReq> receiverList, MessageBodyReq mesg);

    /**
     * 发送消息
     * @param channelTypeEnum 2=飞书
     * @param sender
     * @return
     */
    DubboResponse<MsgSendLogResp> sendMessage(Long tenantId,ChannelTypeEnum channelTypeEnum,MessageUserReq sender,MessageUserReq receiver, MessageBodyReq mesg);
}
