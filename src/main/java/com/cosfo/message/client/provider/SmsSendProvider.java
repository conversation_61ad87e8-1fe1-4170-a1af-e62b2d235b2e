package com.cosfo.message.client.provider;

import com.cosfo.message.client.enums.SMSTypeEnum;
import com.cosfo.message.client.enums.SmsPlatformCodeEnum;
import com.cosfo.message.client.req.SmsBySceneIdReq;
import com.cosfo.message.client.req.SmsByTemplateReq;
import net.xianmu.common.result.DubboResponse;


/**
 * 发送短信
 */
public interface SmsSendProvider {

    /**
     * 根据场景ID发送短信
     * 使用前提 : 消息中心有对应的场景及短信模版的信息
     * 说明:
     *      一个场景可以使用多个渠道(一个渠道对应一个模版)发送短信 由com.cosfo.message.client.req.SmsBySceneIdReq#thirdCompany参数指定,不指定时按照场景查询所有渠道,依次轮循三方渠道发送
     */
    DubboResponse<Void> sendBySceneId(Long tenantId, SmsBySceneIdReq req);

    /**
     * 根据模版发送短信
     * 使用前提 : 消息中心有对应的场景及短信模版的信息
     */
    DubboResponse<Void> sendByTemplate(Long tenantId, SmsByTemplateReq req);

    /**
     * 发送短信
     *      说明:消息中心只充当sendutil,调用者自己指定渠道、模版、参数等信息
     * @param thirdCompany 选择ali 还是创蓝
     * @param sign 短信签名
     * @param templateCode 短信 模版code,ali时必传.
     * @param phone 手机号
     * @param content 模版参数/短信内容
     * @return
     */
    DubboResponse<Void> send(SmsPlatformCodeEnum thirdCompany, SMSTypeEnum smsType, String sign, String templateCode, String phone, String content);
}
