package com.cosfo.message.client.provider;

import com.cosfo.message.client.common.page.req.PageQueryReq;
import com.cosfo.message.client.common.page.resp.PageResp;
import com.cosfo.message.client.req.BatchMarkMsgReq;
import com.cosfo.message.client.req.MsgSendLogQueryReq;
import com.cosfo.message.client.req.NotifyMessageReq;
import com.cosfo.message.client.req.TenantNotifyReq;
import com.cosfo.message.client.resp.MsgNotifySendLogResp;
import com.cosfo.message.client.resp.MsgSendLogResp;
import net.xianmu.common.result.DubboResponse;

import javax.validation.Valid;
import java.util.List;

/**
 * 消息记录
 */
public interface MessageSendLogProvider {

    /**
     * 发结果查询
     */
    DubboResponse<List<MsgSendLogResp>> querySendLog(List<Long> logIds);


    /**
     * 分页查询租户消息列表
     *
     * @param msgSendLogQueryReq
     * @param pageQueryReq
     * @return
     */
    DubboResponse<PageResp<MsgNotifySendLogResp>> page4Manage(@Valid MsgSendLogQueryReq msgSendLogQueryReq, PageQueryReq pageQueryReq);

    /**
     * 查询符合条件的消息数量
     *
     * @param msgSendLogQueryReq
     * @return
     */
    DubboResponse<Integer> countByCondition(@Valid MsgSendLogQueryReq msgSendLogQueryReq);

    /**
     * 批量标记读取状态
     *
     * @param batchMarkMsgReq
     * @return
     */
    DubboResponse<Boolean> markBatch(@Valid BatchMarkMsgReq batchMarkMsgReq);

    /**
     * 创建帆台通知消息
     * @param notifyMessageReq
     * @return
     */
    DubboResponse<Boolean> createNotifyMessage(@Valid NotifyMessageReq notifyMessageReq);

    /**
     * 查询租户提醒通知消息
     * @param tenantNotifyReq
     * @return
     */
    DubboResponse<List<MsgNotifySendLogResp>> queryAlertNotifyMessage(@Valid TenantNotifyReq tenantNotifyReq);
}
