package com.cosfo.message.client.provider;

import com.cosfo.message.client.common.page.req.PageQueryReq;
import com.cosfo.message.client.common.page.resp.PageResp;
import com.cosfo.message.client.req.*;
import com.cosfo.message.client.resp.*;
import net.xianmu.common.result.DubboResponse;

import java.util.List;

public interface NoticeProvider {
    /**
     * 公告处理记录 - 点赞；取消点赞；
     * @param req
     * @return
     */
    DubboResponse<Void> supportMsgReadLog(MsgReadLogReq req);
    /**
     * 浏览公告
     * @param req id不能空
     * @param needSaveLog 是否需要记录 浏览记录，false=不需要；true=需要，req属性需要完整
     * @return
     */
    DubboResponse<NoticeDetailResultResp> getNoticeById(MsgReadLogReq req, Boolean needSaveLog);
    /**
     * 查询用户未读取的公告数量
     * @return
     */
    DubboResponse<Integer> getUnReadNoticeAmountByUserId(Long uId,Long tenantId);
    /**
     * 分页查询公告 管理员查询公告列表
     * @return
     */
    DubboResponse<PageResp<NoticeList4ManagerResultResp>> page4Manager(NoticeQuery4ManagerReq noticeQueryReq, PageQueryReq pageQueryReq);
    /**
     * 分页查询公告 接收者查询公告列表
     * @return
     */
    DubboResponse<PageResp<NoticeList4ReceiverResultResp>> page4Receiver(NoticeQuery4ReceiverReq noticeQueryReq, PageQueryReq pageQueryReq);

    /**
     * 删除公告
     * @return
     */
    DubboResponse<Void> deleteNotic(Long id);
    /**
     * 编辑公告
     * @return
     */
    DubboResponse<Long> editNotic(MsgNoticeEditReq req);
    /**
     * 查询公告的接收者列表
     * @return
     */
    DubboResponse<PageResp<MsgNoticeReceiverResp>> pageReceiver(MsgNoticeReceiverQueryReq req, PageQueryReq pageQueryReq);

    /**
     * 查询浏览/点赞记录
     * @param req
     * @param pageQueryReq
     * @return
     */
    DubboResponse<PageResp<NoticeReadLogResultResp>> pageReadOrSupportLog(ReadOrSupportLogQueryReq req, PageQueryReq pageQueryReq);
}
