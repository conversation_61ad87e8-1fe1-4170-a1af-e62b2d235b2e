package com.cosfo.message.client.resp;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
@Data
public class NoticeReadLogResultResp implements Serializable {
    private static final long serialVersionUID = 1L;

        /**
         * 租户id
         */
        private Long tenantId;

        /**
         * 用户id
         */
        private Long uId;

        /**
         * 用户角色
         */
        private Integer roleType;

        /**
         * 用户名称
         */
        private String uName;

        /**
         * 手机号
         */
        private String phone;

        /**
         * 0=浏览;1=点赞
         */
        private Integer actionType;

        /**
         * 操作时间
         */
        private LocalDateTime time;
}
