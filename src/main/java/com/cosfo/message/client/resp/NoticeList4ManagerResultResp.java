package com.cosfo.message.client.resp;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class NoticeList4ManagerResultResp extends NoticeListResultResp{

    /**
     * 接收对象 ex：所有门店（120）
     */
    private String receiveStore;


    /**
     * 创建用户id
     */
    private Long createUid;

    /**
     * 编辑用户id
     */
    private Long editUid;

    /**
     * 发布用户id
     */
    private Long pushUid;

    /**
     * 发布类型0=定时发布,1=立即发布
     */
    private Integer pushType;
    /**
     * 阅读数
     */
    private Integer readAmount;

    /**
     * 点赞数
     */
    private Integer supportAmount;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
