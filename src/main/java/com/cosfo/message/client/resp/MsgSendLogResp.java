package com.cosfo.message.client.resp;

import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 消息发送记录结果
 * </p>
 *
 * <AUTHOR>
 */
@Data
public class MsgSendLogResp implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 消息发送记录id
     */
    private Long id;
    /**
     * 发送状态0=成功,1=失败,2=发送中
     */
    private Integer sendStatus;
    /**
     * 接收者，baseuserid
     */
    private Long baseUserId;
    /**
     * 接收者，bizuserid
     */
    private Long bizUserId;
    /**
     * 接收者，手机号码
     */
    private String phone;
    /**
     * 接收者，第三方uid
     */
    private String receiveThirdUid;
    /**
     * 失败原因
     */
    private String reason;
}
