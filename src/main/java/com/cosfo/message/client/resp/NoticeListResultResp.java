package com.cosfo.message.client.resp;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 消息列表 基础类，不要随意扩展
 */
@Data
public class NoticeListResultResp implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公告id
     */
    private Long id;
    /**
     * 发布时间
     */
    private LocalDateTime pushTime;
    /**
     * 标题
     */
    private String title;
    /**
     * 内容简略
     */
    private String content;
    /**
     * 消息类型,0=公告消息
     */
    private Integer contentType;
}
