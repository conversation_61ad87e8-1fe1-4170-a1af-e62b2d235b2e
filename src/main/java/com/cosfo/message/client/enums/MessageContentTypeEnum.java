package com.cosfo.message.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: fansongsong
 * @Date: 2023-07-14
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum MessageContentTypeEnum {
    NOTICE(0, "公告消息"),

    NORMAL(1, "普通消息"),

    AFTER_TENANT_NOTIFY(2, "售后租户通知"),

    ALLOT_TENANT_NOTIFY(3, "调拨租户通知"),

    CUSTOM_MESSAGE(4, "客服消息"),
    ;

    private Integer type;
    private String desc;

    public static MessageContentTypeEnum getByType(Integer type) {
        for (MessageContentTypeEnum messageContentTypeEnum : MessageContentTypeEnum.values()) {
            if (messageContentTypeEnum.type.equals(type)) {
                return messageContentTypeEnum;
            }
        }

        return null;
    }
}
