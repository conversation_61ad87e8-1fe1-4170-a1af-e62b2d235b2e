package com.cosfo.message.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: fansongsong
 * @Date: 2023-07-20
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum KeyCodeEnum {

    /**
     * 售后退款通知all
     */
    AFTER_SALE_NO("00001", "售后单号"),

    REFUND_PRICE("00002", "退款金额"),

    UNDERFUNDED_ACCOUNT("00003", "资金不足账号"),

    /**
     * 调拨通知
     */
    ALLOT_BEFORE_TIME("00100", "变更前预约出库时间"),

    ALLOT_AFTER_TIME("00101", "变更后预约出库时间"),

    ALLOT_NO("00102", "调拨单号"),

    ALLOT_BEFORE_IN_STORAGE_TIME("00103", "变更前预约入库时间"),

    ALLOT_AFTER_IN_STORAGE_TIME("00104", "变更前预约入库时间"),

    ;

    private String code;
    private String desc;

    public static KeyCodeEnum getByCode(String code) {
        for (KeyCodeEnum keyCodeEnum : KeyCodeEnum.values()) {
            if (keyCodeEnum.code.equals(code)) {
                return keyCodeEnum;
            }
        }

        return null;
    }
}
