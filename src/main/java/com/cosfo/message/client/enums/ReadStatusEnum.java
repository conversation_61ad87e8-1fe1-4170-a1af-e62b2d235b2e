package com.cosfo.message.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: fansongsong
 * @Date: 2023-07-18
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum ReadStatusEnum {
    UN_READ(0, "未读"),

    READ(1, "已读"),

    ;

    private Integer status;
    private String desc;

    public static ReadStatusEnum getByStatus(Integer status) {
        for (ReadStatusEnum readStatusEnum : ReadStatusEnum.values()) {
            if (readStatusEnum.status.equals(status)) {
                return readStatusEnum;
            }
        }

        return null;
    }
}
