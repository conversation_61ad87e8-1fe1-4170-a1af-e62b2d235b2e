package com.cosfo.message.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: fansongsong
 * @Date: 2023-10-17
 * @Description:
 */
public interface TemplateWechatEnum {

    @Getter
    @AllArgsConstructor
    enum TemplateCode {

        SAAS_PAY_SUCCESS_SUPPLIER_ORDER_CODE("SAAS_PAY_SUCCESS_SUPPLIER_ORDER_CODE","{\"amount13\":{\"value\":\"%s\"},\"time14\":{\"value\":\"%s\"},\"const16\":{\"value\":\"待发货\"}}", "每单待配通知"),

        SAAS_ORDER_SUPPLIER_REFUND_CODE("SAAS_ORDER_SUPPLIER_REFUND_CODE","{\"amount2\":{\"value\":\"%s\"},\"time3\":{\"value\":\"%s\"},\"character_string1\":{\"value\":\"%s\"}}", "退款通知(整单退)"),

        SAAS_ORDER_SUPPLIER_TOTAL_CODE("SAAS_ORDER_SUPPLIER_TOTAL_CODE","{\"thing8\":{\"value\":\"%s\"},\"amount12\":{\"value\":\"%s\"},\"thing10\":{\"value\":\"%s\"}}", "每天待配送整点通知"),

        ;
        private String code;

        private String keyword;

        private String content;


    }
}
