package com.cosfo.message.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: fansongsong
 * @Date: 2023-07-14
 * @Description:飞书发送消息类型
 */
@Getter
@AllArgsConstructor
public enum MsgBodyTypeEnum {

    TEXT(0, "text","文本"),

    POST(1, "post","富文本"),

    IMAGE(2,"image" ,"图片"),

    INTERACTIVE(3,"interactive" ,"消息卡片"),

    MEDIA(4,"media" ,"视频"),

    FILE(5, "file","文件"),

    VOICE(6, "voice","语音"),

    VIDEO(7, "video","视频客服消息"),

    MUSIC(8, "music","音乐"),

    ;

    private Integer type;

    private String msgType;

    private String desc;

    public static MsgBodyTypeEnum getByType(Integer typeNum) {
        for (MsgBodyTypeEnum type : MsgBodyTypeEnum.values()) {
            if (type.type.equals(typeNum)) {
                return type;
            }
        }
        return null;
    }
}
