package com.cosfo.message.client.common;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符串模板工具类
 * 用于处理字符串中的占位符替换
 */
public class StringTemplateUtils {

    /**
     * 匹配 ${变量名} 格式的占位符
     */
    private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\$\\{[^}]*\\}");

    /**
     * 将字符串模板中的占位符按顺序替换为列表中的值
     * 
     * @param template 包含占位符的模板字符串，如："${one},你好,由${two}公司收款更改为${three}公司收款"
     * @param values   要替换的值列表，按顺序对应占位符
     * @return 替换后的字符串
     * @throws IllegalArgumentException 当占位符数量与值列表大小不匹配时抛出
     * 
     * 示例：
     * String template = "${one},你好,由${two}公司收款更改为${three}公司收款";
     * List<String> values = Arrays.asList("1", "heihei", "xixi");
     * String result = replacePlaceholders(template, values);
     * // 结果: "1,你好,由heihei公司收款更改为xixi公司收款"
     */
    public static String replacePlaceholders(String template, List<String> values) {
        if (template == null) {
            return null;
        }
        
        if (values == null || values.isEmpty()) {
            return template;
        }

        Matcher matcher = PLACEHOLDER_PATTERN.matcher(template);
        StringBuffer result = new StringBuffer();
        int valueIndex = 0;

        while (matcher.find()) {
            if (valueIndex >= values.size()) {
                throw new IllegalArgumentException(
                    String.format("占位符数量(%d)超过了提供的值数量(%d)", 
                        countPlaceholders(template), values.size())
                );
            }
            
            String replacement = values.get(valueIndex);
            // 处理null值
            if (replacement == null) {
                replacement = "";
            }
            
            // 转义特殊字符，避免在替换时出现问题
            replacement = Matcher.quoteReplacement(replacement);
            matcher.appendReplacement(result, replacement);
            valueIndex++;
        }
        
        matcher.appendTail(result);
        
        // 检查是否还有未使用的值
        if (valueIndex < values.size()) {
            throw new IllegalArgumentException(
                String.format("提供的值数量(%d)超过了占位符数量(%d)", 
                    values.size(), countPlaceholders(template))
            );
        }
        
        return result.toString();
    }

    /**
     * 计算模板中占位符的数量
     * 
     * @param template 模板字符串
     * @return 占位符数量
     */
    private static int countPlaceholders(String template) {
        if (template == null) {
            return 0;
        }
        
        Matcher matcher = PLACEHOLDER_PATTERN.matcher(template);
        int count = 0;
        while (matcher.find()) {
            count++;
        }
        return count;
    }

    /**
     * 检查模板字符串中是否包含占位符
     * 
     * @param template 模板字符串
     * @return 如果包含占位符返回true，否则返回false
     */
    public static boolean hasPlaceholders(String template) {
        if (template == null) {
            return false;
        }
        return PLACEHOLDER_PATTERN.matcher(template).find();
    }
}
