package com.cosfo.message.client.req;

import com.cosfo.message.client.enums.SmsSceneCodeEnum;
import com.cosfo.message.client.enums.SmsPlatformCodeEnum;
import lombok.Data;
import lombok.NonNull;

import java.io.Serializable;
import java.util.List;


@Data
public class SmsBySceneIdReq extends SmsBaseReq implements Serializable {
    /**
     * 场景
     */
    @NonNull
    private SmsSceneCodeEnum sceneCode;
    /**
     * 三方公司选择
     *   可空 空时将轮询不同的三方公司发送
     */
    private List<SmsPlatformCodeEnum> platformCodeEnums;


}
