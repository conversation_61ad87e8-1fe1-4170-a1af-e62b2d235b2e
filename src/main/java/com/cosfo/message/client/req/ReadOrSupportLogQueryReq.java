package com.cosfo.message.client.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ReadOrSupportLogQueryReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 公告id
     */
    @NotNull(message = "公告id不能为空")
    private Long noticeId;
    /**
     * 记录类型 0=浏览;1=点赞
     */
    private Integer actionType;

    /**
     * 租户id
     */
    private Long tenantId;
}
