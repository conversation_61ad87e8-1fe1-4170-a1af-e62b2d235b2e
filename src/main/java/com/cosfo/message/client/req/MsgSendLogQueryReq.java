package com.cosfo.message.client.req;

import com.cosfo.message.client.enums.ChannelTypeEnum;
import com.cosfo.message.client.enums.MessageContentTypeEnum;
import com.cosfo.message.client.enums.ReadStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: fansongsong
 * @Date: 2023-07-18
 * @Description:
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MsgSendLogQueryReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息类型(入参已失效)
     */
    @Deprecated
    private MessageContentTypeEnum messageContentTypeEnum;

    /**
     * 渠道类型,为空默认查询帆台
     */
    private ChannelTypeEnum channelTypeEnum;

    /**
     * 开始日期
     */
    private LocalDateTime startTime;

    /**
     * 结束日期
     */
    private LocalDateTime endTime;

    /**
     * 读取状态0=未读,1=已读
     */
    private ReadStatusEnum readStatusEnum;

    /**
     * 租户id
     */
    @NotNull(message = "tenantId不能为空")
    private Long tenantId;
}
