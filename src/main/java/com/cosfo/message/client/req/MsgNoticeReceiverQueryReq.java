package com.cosfo.message.client.req;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class MsgNoticeReceiverQueryReq implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 0=店铺
     */
    private Integer receiverType;
    /**
     * 接受者id
     */
    private List<Long> receiverIds;
    /**
     * 公告id
     */
    @NotNull(message = "公告id不能为空")
    private Long noticeId;
    /**
     * 是否点赞 false=未点赞
     */
    private Boolean supportFlag;
    /**
     * 是否已读 false=未读
     */
    private Boolean readFlag;
}
