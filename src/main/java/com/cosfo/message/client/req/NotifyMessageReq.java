package com.cosfo.message.client.req;

import com.cosfo.message.client.enums.MessageContentTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-07-20
 * @Description:
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class NotifyMessageReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    @NotNull(message = "tenantId不能为空")
    private Long tenantId;

    /**
     * 消息类型
     */
    @NotNull(message = "messageContentTypeEnum不能为空")
    private MessageContentTypeEnum messageContentTypeEnum;

    /**
     * 标题
     */
    @NotNull(message = "标题不能为空")
    @Length(max = 64, message = "标题长度不可超过64")
    private String title;

    /**
     * 详细内容
     *
     */
    private List<NotifyTipBodyReq> detailList;

    /**
     * 跳转ID
     */
    private Long pageId;

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 幂等ID(相同单号只会创建一条数据，非必填，长度不大于64位)
     */
    private String uniqueId;

}
