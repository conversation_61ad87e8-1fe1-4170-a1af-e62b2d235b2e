package com.cosfo.message.client.req;

import com.cosfo.message.client.enums.JumpUrlTypeEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class MessageBodyReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 消息类型,0=公告消息，1=普通消息
     * {@link com.cosfo.message.client.enums.MessageContentTypeEnum
     */
    private Integer contentType;
    /**
     * 消息类型,0=文本，1=富文本，2=图片，3=消息卡片，4=视频，5=文件，
     *        6=语音，7=视频客服消息，8=音乐
     *        飞书消息必传,支持:0~5
     *        客服消息必传，支持:0、2、6、7、8
     * {@link com.cosfo.message.client.enums.MsgBodyTypeEnum
     */
    private Integer msgBodyType;
    /**
     * 标题(可空,长度限制64)
     */
    private String title;
    /**
     * 内容
     * 若是飞书消息，data为发送的具体content内容，如:{"text":" test content"}
     * 若是客服消息，data为发送的具体content内容，如:{"content":"Hello World"}
     */
    private String data;
    /**
     * 模版id(公众号消息通知时，传公众号模版ID，templateId、templateCode二选一)
     */
    private String templateId;
    /**
     * 模版编码(公众号消息通知时，传公众号模版ID，templateId、templateCode二选一)
     */
    private String templateCode;
    /**
     * 跳转类型 1:网页/2:小程序
     */
    private JumpUrlTypeEnum jumpUrlTypeEnum;
    /**
     * 消息 点击 跳转url
     * 小程序   ex： {"appid":"xiaochengxuappid12345","pagepath":"index?foo=bar"}
     */
    private String jumpUrl;
    /**
     * 微信公众号防重入id。对于同一个openid + client_msg_id, 只发送一条消息,10分钟有效,超过10分钟不保证效果。若无防重入需求，可不填
     */
    private String clientMsgId;
}
