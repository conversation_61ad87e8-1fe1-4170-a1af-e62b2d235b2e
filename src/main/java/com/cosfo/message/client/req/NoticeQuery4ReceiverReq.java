package com.cosfo.message.client.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
public class NoticeQuery4ReceiverReq implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公告id
     */
    private Set<Long> ids;
    /**
     * 读取状态0=未读,1=已读
     */
    private Integer readStatus;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 接收人
     */
    private Long recevieUid;
}
