package com.cosfo.message.client.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: fansongsong
 * @Date: 2023-08-01
 * @Description:
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class TenantNotifyReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    @NotNull(message = "tenantId不能为空")
    private Long tenantId;
}
