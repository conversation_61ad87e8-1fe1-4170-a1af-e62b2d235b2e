package com.cosfo.message.client.req;

import lombok.Data;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;

import java.io.Serializable;

/**
 * 说明：
 * 使用公司内部用户id 或者 三方系统用户id都可以
     * 当使用公司内部用户id发送消息时，(baseuserid,bizUserId,phone 三者选填，不可同时为空)，tenantId，systemOriginType四个参数都不可为空
     * 当使用三方系统用户id发送消息时，thridUid 不可为空，
     * 同时存在时已三方系统用户id为第一优先级
 */
@Data
public class MessageUserReq implements Serializable {
    /**
     * baseuserid
     */
    private Long baseUserId;
    /**
     * bizuserid
     */
    private Long bizUserId;
    /**
     * 手机号码
     */
    private String phone;
    /**
     * 系统来源 @Link net.xianmu.common.enums.base.auth.SystemOriginEnum
     */
    private SystemOriginEnum systemOriginEnum;
}
