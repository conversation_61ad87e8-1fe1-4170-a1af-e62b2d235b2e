package com.cosfo.message.client.req;

import com.cosfo.message.client.enums.SMSTypeEnum;
import lombok.Data;
import lombok.NonNull;

import java.io.Serializable;
import java.util.List;

@Data
public class SmsBaseReq implements Serializable {
    /**
     * 短信类型
     * @see SMSTypeEnum
     */
    private SMSTypeEnum smsType;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 短信内容
     */
    private String msg;
    /**
     * 短信模版参数 需要消息中心根据模版内容进行拼接的参数list
     *     eg:
     *        模版 = ${one},你好,由${two}公司收款更改为${three}公司收款
     *        短信 = 李先生,你好,由大番茄公司收款更改为小地瓜公司收款
     *        msg = [李先生,大番茄,小地瓜]
     */
    private List<String> templateArg;
}
