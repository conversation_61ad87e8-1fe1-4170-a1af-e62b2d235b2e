package com.cosfo.message.client.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class MsgReadLogReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 公告id
     */
    @NotNull(message = "公告id不能空")
    private Long noticeId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 用户id
     */
    private Long uId;

    /**
     * 用户角色
     */
    private Integer roleType;

    /**
     * 用户名称
     */
    private String uName;

    /**
     * 店铺id
     */
    private Long storeId;

    /**
     * 手机号
     */
    private String phone;
    /**
     * 1=点赞；2=取消点赞
     */
    private Integer actionType;
}

